from .type import Header
from typing import Any
from httpx import AsyncClient, Timeout
import logging
from .config import SETTINGS

logger = logging.getLogger(__name__)


class RestUtils:

    def _get_timeout_config(self):
        """Get timeout configuration from settings"""
        return Timeout(
            connect=SETTINGS.HTTP_CONNECT_TIMEOUT,
            read=SETTINGS.HTTP_READ_TIMEOUT,
            write=SETTINGS.HTTP_WRITE_TIMEOUT,
            pool=SETTINGS.HTTP_POOL_TIMEOUT
        )

    async def get(self, url: str, headers: Header) -> Any:
        async with AsyncClient(timeout=self._get_timeout_config()) as client:
            logger.info(f"Getting data from {url}")
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            logger.info(
                f"Data received from {url}. Received data size: {len(response.content)}"
            )
            return response.json()

    async def post(self, url: str, headers: Header, data: Any) -> Any:
        async with Async<PERSON>lient(timeout=self._get_timeout_config()) as client:
            logger.info(f"Posting data to {url}")
            response = await client.post(url, headers=headers, json=data)
            print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
            print(response.content, data)
            response.raise_for_status()
            logger.info(
                f"Data received from {url}. Received data size: {len(response.content)}"
            )
            return response.json()
